(()=>{"use strict";var r,e={186:()=>{const r=window.wp.blocks,e=window.wp.i18n,o=window.wp.blockEditor,i=window.ReactJSXRuntime,n=JSON.parse('{"UU":"myblocks/myheader"}');(0,r.registerBlockType)(n.UU,{edit:function(){return(0,i.jsx)("p",{...(0,o.useBlockProps)(),children:(0,e.__)("Myheader – hello from the editor!","myheader")})}})}},o={};function i(r){var n=o[r];if(void 0!==n)return n.exports;var t=o[r]={exports:{}};return e[r](t,t.exports,i),t.exports}i.m=e,r=[],i.O=(e,o,n,t)=>{if(!o){var a=1/0;for(p=0;p<r.length;p++){for(var[o,n,t]=r[p],l=!0,s=0;s<o.length;s++)(!1&t||a>=t)&&Object.keys(i.O).every(r=>i.O[r](o[s]))?o.splice(s--,1):(l=!1,t<a&&(a=t));if(l){r.splice(p--,1);var h=n();void 0!==h&&(e=h)}}return e}t=t||0;for(var p=r.length;p>0&&r[p-1][2]>t;p--)r[p]=r[p-1];r[p]=[o,n,t]},i.o=(r,e)=>Object.prototype.hasOwnProperty.call(r,e),(()=>{var r={207:0,599:0};i.O.j=e=>0===r[e];var e=(e,o)=>{var n,t,[a,l,s]=o,h=0;if(a.some(e=>0!==r[e])){for(n in l)i.o(l,n)&&(i.m[n]=l[n]);if(s)var p=s(i)}for(e&&e(o);h<a.length;h++)t=a[h],i.o(r,t)&&r[t]&&r[t][0](),r[t]=0;return i.O(p)},o=globalThis.webpackChunkmyheader=globalThis.webpackChunkmyheader||[];o.forEach(e.bind(null,0)),o.push=e.bind(null,o.push.bind(o))})();var n=i.O(void 0,[599],()=>i(186));n=i.O(n)})();